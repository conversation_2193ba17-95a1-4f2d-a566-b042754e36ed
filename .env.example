# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=smart_city_os
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration (for alerts)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# IoT Simulation Configuration
IOT_API_ENDPOINT=http://localhost:3000/api/sensors
IOT_UPDATE_INTERVAL=5000
IOT_SENSORS_COUNT=50

# Analytics Service Configuration
ANALYTICS_PORT=5000
ANALYTICS_MODEL_PATH=./models
ANALYTICS_RETRAIN_INTERVAL=86400

# Blockchain Configuration
SOLANA_NETWORK=devnet
SOLANA_RPC_URL=https://api.devnet.solana.com
WALLET_PRIVATE_KEY=your-wallet-private-key

# Frontend Configuration
REACT_APP_API_URL=http://localhost:3000/api
REACT_APP_WS_URL=ws://localhost:3000
REACT_APP_MAPBOX_TOKEN=your-mapbox-token

# Alert Thresholds
TRAFFIC_CONGESTION_THRESHOLD=80
WASTE_FILL_THRESHOLD=85
AIR_QUALITY_THRESHOLD=150
NOISE_LEVEL_THRESHOLD=70

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
