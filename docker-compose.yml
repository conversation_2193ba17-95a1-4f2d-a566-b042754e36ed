version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: smart_city_db
    environment:
      POSTGRES_DB: smart_city_os
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database:/docker-entrypoint-initdb.d
    networks:
      - smart_city_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    container_name: smart_city_backend
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: smart_city_os
      DB_USER: postgres
      DB_PASSWORD: password
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_EXPIRES_IN: 24h
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - smart_city_network
    volumes:
      - ./backend:/app
      - /app/node_modules
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # IoT Simulation Service
  iot-simulation:
    build:
      context: .
      dockerfile: docker/iot-simulation.Dockerfile
    container_name: smart_city_iot_sim
    environment:
      IOT_API_ENDPOINT: http://backend:3000/api/sensors
      IOT_UPDATE_INTERVAL: 5000
      IOT_SENSORS_COUNT: 50
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - smart_city_network
    volumes:
      - ./iot-simulation:/app
    restart: unless-stopped

  # Frontend (React App)
  frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: smart_city_frontend
    environment:
      REACT_APP_API_URL: http://localhost:3000/api
      REACT_APP_WS_URL: ws://localhost:3000
    ports:
      - "3001:3000"
    depends_on:
      - backend
    networks:
      - smart_city_network
    volumes:
      - ./frontend:/app
      - /app/node_modules

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: smart_city_redis
    ports:
      - "6379:6379"
    networks:
      - smart_city_network
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # Analytics Service (Python)
  analytics:
    build:
      context: .
      dockerfile: docker/analytics.Dockerfile
    container_name: smart_city_analytics
    environment:
      ANALYTICS_PORT: 5000
      DATABASE_URL: ********************************************/smart_city_os
    ports:
      - "5000:5000"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - smart_city_network
    volumes:
      - ./analytics:/app
      - analytics_models:/app/models

volumes:
  postgres_data:
  redis_data:
  analytics_models:

networks:
  smart_city_network:
    driver: bridge
