import React, { useState, useEffect } from 'react';
import { useSocket } from '../contexts/SocketContext';
import { useQuery } from 'react-query';
import { sensorService, alertService } from '../services/api';
import { motion } from 'framer-motion';
import {
  CpuChipIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  SignalIcon,
  MapPinIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import StatCard from '../components/StatCard';
import SensorMap from '../components/SensorMap';
import RealtimeChart from '../components/RealtimeChart';
import AlertsList from '../components/AlertsList';
import LoadingSpinner from '../components/LoadingSpinner';

function Dashboard() {
  const { realtimeData, isConnected, getActiveAlerts } = useSocket();
  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');

  // Fetch initial data
  const { data: sensorsData, isLoading: sensorsLoading } = useQuery(
    'sensors',
    () => sensorService.getSensors({ limit: 100 }),
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  const { data: alertsData, isLoading: alertsLoading } = useQuery(
    ['alerts', selectedTimeRange],
    () => alertService.getAlertStats(selectedTimeRange),
    {
      refetchInterval: 10000, // Refetch every 10 seconds
    }
  );

  const sensors = sensorsData?.data?.sensors || [];
  const alertStats = alertsData?.data || {};
  const activeAlerts = getActiveAlerts();

  // Calculate statistics
  const stats = {
    totalSensors: sensors.length,
    activeSensors: sensors.filter(s => s.status === 'active').length,
    offlineSensors: sensors.filter(s => s.status === 'offline').length,
    maintenanceSensors: sensors.filter(s => s.status === 'maintenance').length,
    totalAlerts: activeAlerts.length,
    criticalAlerts: activeAlerts.filter(a => a.severity === 'critical').length,
    highAlerts: activeAlerts.filter(a => a.severity === 'high').length,
    mediumAlerts: activeAlerts.filter(a => a.severity === 'medium').length,
  };

  // Sensor type distribution
  const sensorTypes = sensors.reduce((acc, sensor) => {
    acc[sensor.type] = (acc[sensor.type] || 0) + 1;
    return acc;
  }, {});

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (sensorsLoading || alertsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants} className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time city monitoring and analytics
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Connection status */}
          <div className={`flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
            isConnected 
              ? 'bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200'
              : 'bg-danger-100 text-danger-800 dark:bg-danger-900 dark:text-danger-200'
          }`}>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-success-500' : 'bg-danger-500'}`} />
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>

          {/* Time range selector */}
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="input text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
      </motion.div>

      {/* Statistics Cards */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Sensors"
          value={stats.totalSensors}
          icon={CpuChipIcon}
          color="primary"
          subtitle={`${stats.activeSensors} active`}
          trend={stats.activeSensors > stats.offlineSensors ? 'up' : 'down'}
        />
        
        <StatCard
          title="Active Alerts"
          value={stats.totalAlerts}
          icon={ExclamationTriangleIcon}
          color="warning"
          subtitle={`${stats.criticalAlerts} critical`}
          trend={stats.criticalAlerts > 0 ? 'down' : 'up'}
        />
        
        <StatCard
          title="System Health"
          value={`${Math.round((stats.activeSensors / stats.totalSensors) * 100)}%`}
          icon={SignalIcon}
          color="success"
          subtitle="Operational"
          trend="up"
        />
        
        <StatCard
          title="Data Points"
          value={Object.keys(realtimeData.sensors).length}
          icon={ChartBarIcon}
          color="info"
          subtitle="Real-time"
          trend="up"
        />
      </motion.div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sensor Map */}
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MapPinIcon className="w-5 h-5 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Sensor Locations
                  </h3>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {sensors.length} sensors
                </span>
              </div>
            </div>
            <div className="card-body p-0">
              <SensorMap sensors={sensors} realtimeData={realtimeData.sensors} />
            </div>
          </div>
        </motion.div>

        {/* Active Alerts */}
        <motion.div variants={itemVariants}>
          <div className="card h-full">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <ExclamationTriangleIcon className="w-5 h-5 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Active Alerts
                  </h3>
                </div>
                <span className="badge badge-warning">
                  {activeAlerts.length}
                </span>
              </div>
            </div>
            <div className="card-body p-0">
              <AlertsList alerts={activeAlerts.slice(0, 10)} compact />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Real-time Data Chart */}
        <motion.div variants={itemVariants}>
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <ChartBarIcon className="w-5 h-5 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    Real-time Metrics
                  </h3>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse" />
                  <span className="text-sm text-gray-500 dark:text-gray-400">Live</span>
                </div>
              </div>
            </div>
            <div className="card-body">
              <RealtimeChart data={realtimeData.sensors} />
            </div>
          </div>
        </motion.div>

        {/* Sensor Types Distribution */}
        <motion.div variants={itemVariants}>
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Sensor Distribution
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                {Object.entries(sensorTypes).map(([type, count]) => {
                  const percentage = (count / stats.totalSensors) * 100;
                  const colors = {
                    traffic: 'bg-blue-500',
                    waste: 'bg-green-500',
                    air_quality: 'bg-purple-500',
                    noise: 'bg-yellow-500',
                    water_quality: 'bg-cyan-500',
                    energy: 'bg-orange-500',
                    parking: 'bg-pink-500'
                  };
                  
                  return (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${colors[type] || 'bg-gray-500'}`} />
                        <span className="text-sm font-medium text-gray-900 dark:text-white capitalize">
                          {type.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {count}
                        </span>
                        <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                          <motion.div
                            className={`h-2 rounded-full ${colors[type] || 'bg-gray-500'}`}
                            initial={{ width: 0 }}
                            animate={{ width: `${percentage}%` }}
                            transition={{ duration: 1, delay: 0.2 }}
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <div className="card">
          <div className="card-header">
            <div className="flex items-center space-x-2">
              <ClockIcon className="w-5 h-5 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Recent Activity
              </h3>
            </div>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              {Object.entries(realtimeData.sensors)
                .slice(0, 5)
                .map(([sensorId, data]) => (
                  <div key={sensorId} className="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-success-500 rounded-full" />
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {sensorId}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          Data received
                        </p>
                      </div>
                    </div>
                    <span className="text-xs text-gray-400">
                      {data.timestamp ? new Date(data.timestamp).toLocaleTimeString() : 'Now'}
                    </span>
                  </div>
                ))}
              
              {Object.keys(realtimeData.sensors).length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <CpuChipIcon className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No recent sensor activity</p>
                  <p className="text-sm">Start the IoT simulation to see real-time data</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}

export default Dashboard;
