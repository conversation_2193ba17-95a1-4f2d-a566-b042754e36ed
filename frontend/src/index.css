@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

/* Base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  .dark body {
    @apply bg-gray-900 text-gray-100;
  }
}

/* Component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
  }
  
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .dark .card-header {
    @apply border-gray-700;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .dark .input {
    @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-info {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .dark .badge-success {
    @apply bg-success-900 text-success-200;
  }
  
  .dark .badge-warning {
    @apply bg-warning-900 text-warning-200;
  }
  
  .dark .badge-danger {
    @apply bg-danger-900 text-danger-200;
  }
  
  .dark .badge-info {
    @apply bg-primary-900 text-primary-200;
  }
  
  .stat-card {
    @apply card p-6 hover:shadow-lg transition-shadow duration-200;
  }
  
  .stat-value {
    @apply text-3xl font-bold text-gray-900;
  }
  
  .dark .stat-value {
    @apply text-white;
  }
  
  .stat-label {
    @apply text-sm font-medium text-gray-500 uppercase tracking-wide;
  }
  
  .dark .stat-label {
    @apply text-gray-400;
  }
  
  .stat-change {
    @apply text-sm font-medium;
  }
  
  .stat-change.positive {
    @apply text-success-600;
  }
  
  .stat-change.negative {
    @apply text-danger-600;
  }
  
  .dark .stat-change.positive {
    @apply text-success-400;
  }
  
  .dark .stat-change.negative {
    @apply text-danger-400;
  }
}

/* Utility styles */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-purple-600 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-purple-600;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-gray-700 to-gray-900;
  }
  
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
  
  .dark .glass {
    @apply bg-gray-800/80 border-gray-700/20;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

.dark ::-webkit-scrollbar-track {
  @apply bg-gray-800;
}

.dark ::-webkit-scrollbar-thumb {
  @apply bg-gray-600;
}

.dark ::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

/* Leaflet map customization */
.leaflet-container {
  @apply rounded-lg;
}

.leaflet-popup-content-wrapper {
  @apply rounded-lg shadow-lg;
}

.leaflet-popup-content {
  @apply text-sm;
}

/* Chart.js customization */
.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Loading animations */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
}

.loading-dots {
  @apply flex space-x-1;
}

.loading-dots div {
  @apply w-2 h-2 bg-primary-600 rounded-full animate-bounce;
}

.loading-dots div:nth-child(2) {
  animation-delay: 0.1s;
}

.loading-dots div:nth-child(3) {
  animation-delay: 0.2s;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .card {
    @apply rounded-lg;
  }
  
  .card-body {
    @apply px-4 py-3;
  }
  
  .card-header {
    @apply px-4 py-3;
  }
}
