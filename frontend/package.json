{"name": "smart-city-dashboard", "version": "1.0.0", "description": "Smart City OS Dashboard - Modern React Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.14.2", "axios": "^1.4.0", "socket.io-client": "^4.7.2", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "chart.js": "^4.3.3", "react-chartjs-2": "^5.2.0", "date-fns": "^2.30.0", "recharts": "^2.7.2", "@heroicons/react": "^2.0.18", "@headlessui/react": "^1.7.15", "clsx": "^2.0.0", "framer-motion": "^10.16.1", "react-hot-toast": "^2.4.1", "react-hook-form": "^7.45.2", "@hookform/resolvers": "^3.1.1", "yup": "^1.2.0", "tailwindcss": "^3.3.3", "@tailwindcss/forms": "^0.5.4", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "lucide-react": "^0.263.1", "react-query": "^3.39.3", "zustand": "^4.4.1", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "react-scripts start", "preview": "serve -s build -l 3001"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"serve": "^14.2.0"}, "proxy": "http://localhost:3000"}